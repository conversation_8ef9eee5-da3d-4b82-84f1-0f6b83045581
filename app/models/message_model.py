"""
MongoDB model definition for messages in the communication service.

This module defines the Message document model and related enums using mongoengine ODM.
It includes comprehensive validation for different message types and their associated
JSON data content.
"""

# Standard library imports
from datetime import datetime

# Third-party imports
from google.protobuf import struct_pb2
from mongoengine import DateTimeField, DictField, Document, ListField, ObjectIdField, StringField
from mongoengine.errors import ValidationError

# Local imports
from app.grpc_ import communication_pb2


# Message model
class Message(Document):
    """
    MongoDB document model for messages in conversations.

    This model represents a message sent by a user or agent in a conversation,
    storing metadata such as sender information, JSON message data, and timestamps.

    Attributes:
        conversationId (ObjectIdField): The ID of the conversation to which the message belongs.
        senderType (StringField): The type of the sender (user or agent).
        data (DictField): The JSON data of the message (can contain any valid JSON structure).
        workflowId (StringField): The ID of the workflow associated with the message.
        workflowResponse (ListField): Array of workflow responses (can contain anything).
        status (StringField): The status of the message (running, completed).
        type (StringField): The type of the message (chat, mcp, workflow, user_message).
        createdAt (DateTimeField): Timestamp for when the message was created.
        updatedAt (DateTimeField): Timestamp for when the message was last updated.
    """

    # MongoDB collection & index configuration
    meta = {
        "collection": "messages",
        "indexes": [
            # Single field indexes for the frequent queries
            "conversationId",
            "senderType",
            "type",
            "status",
            "workflowId",
            # Text index for full-text search in message data
            {"fields": ["$**"], "default_language": "english", "cls": False},
            # Compound indexes for time-based queries
            ("conversationId", "createdAt"),
            ("conversationId", "type"),
            ("conversationId", "senderType"),
            ("conversationId", "status"),
            ("conversationId", "workflowId"),
            # Enhanced compound indexes for advanced filtering
            ("conversationId", "type", "createdAt"),
            ("conversationId", "senderType", "createdAt"),
            ("conversationId", "status", "createdAt"),
            ("conversationId", "type", "senderType", "createdAt"),
            # Date range optimization indexes
            ("conversationId", "createdAt", "type"),
            ("conversationId", "updatedAt", "type"),
        ],
        "ordering": ["-createdAt"],
    }

    # Conversation ID field
    conversationId = ObjectIdField(required=True)

    # Sender type field
    senderType = StringField(required=True)

    # Content field - now stores JSON data as a dictionary
    data = DictField(required=False, null=True)

    # Workflow ID field
    workflowId = StringField(null=True)

    # Workflow response field - changed to array
    workflowResponse = ListField(default=list, null=True)

    # Status field - nullable with choices from protobuf enum
    status = StringField(null=True)

    # Type field - required with choices from protobuf enum
    type = StringField(required=True)

    # Timestamp field for the creation of the message
    createdAt = DateTimeField(default=datetime.utcnow)

    # Timestamp field for the last update to the message
    updatedAt = DateTimeField(default=datetime.utcnow)

    # Clean the message model
    def clean(self):
        """Update the updated timestamp before saving the message."""

        # If the creation timestamp is not set
        if not self.createdAt:
            # Set the creation timestamp to the current time
            self.createdAt = datetime.utcnow()

        # Update the updated timestamp
        self.updatedAt = datetime.utcnow()

        # Get enum names
        sender_type_names = [name for name, _ in communication_pb2.SenderType.items()]

        # Validate the sender type
        if self.senderType not in sender_type_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid sender type: {self.senderType}. Must be one of: {sender_type_names}"
            )

        # Validate message type
        message_type_names = [name for name, _ in communication_pb2.MessageType.items()]
        # Remove UNSPECIFIED as it's not a valid choice for users
        valid_message_types = [
            name for name in message_type_names if name != "MESSAGE_TYPE_UNSPECIFIED"
        ]
        if self.type not in valid_message_types:
            raise ValidationError(
                f"Invalid message type: {self.type}. Must be one of: {valid_message_types}"
            )

        # Validate message status if provided
        if self.status is not None:
            message_status_names = [name for name, _ in communication_pb2.MessageStatus.items()]
            # Remove UNSPECIFIED as it's not a valid choice for users
            valid_message_statuses = [
                name for name in message_status_names if name != "MESSAGE_STATUS_UNSPECIFIED"
            ]
            if self.status not in valid_message_statuses:
                raise ValidationError(
                    f"Invalid message status: {self.status}. Must be one of: {valid_message_statuses}"
                )

        # Call the parent clean method
        super().clean()

    # String representation of the message document
    def __str__(self):
        """Return the string representation of the message document."""

        # Return the string representation of the message document
        data_preview = str(self.data)[:50] + "..." if self.data else "None"
        return f"Message({self.id}, {self.senderType}, {self.type}, {data_preview})"

    # Convert the message document to a dictionary
    def to_dict(self):
        """Convert the message document to a dictionary."""

        # Return the message document as a dictionary
        return {
            "id": str(self.id),
            "conversationId": str(self.conversationId),
            "senderType": self.senderType,
            "data": self.data,
            "workflowId": self.workflowId,
            "workflowResponse": self.workflowResponse,
            "status": self.status,
            "type": self.type,
            "createdAt": self.createdAt,
            "updatedAt": self.updatedAt,
        }

    # Convert the message document to a gRPC message
    def to_proto(self):
        """Convert the message document to a gRPC message."""

        # Create a new message message
        message = communication_pb2.Message()

        # Set the message ID
        from app.utils.logger import setup_logger

        logger = setup_logger("communication-service/models/message_model.py")

        # Set the message ID
        try:
            message.id = str(self.id)
        except Exception as e:
            logger.error("Error setting id", value=repr(self.id), error=str(e))

        # Set the conversation ID
        try:
            message.conversationId = str(self.conversationId)
        except Exception as e:
            logger.error(
                "Error setting conversationId",
                value=repr(self.conversationId),
                error=str(e),
            )

        # Convert string enums to int for protobuf
        try:
            message.senderType = communication_pb2.SenderType.Value(self.senderType)
        except Exception as e:
            logger.warning(
                "Invalid senderType for protobuf enum, defaulting to SENDER_TYPE_UNSPECIFIED",
                senderType=str(self.senderType),
                error=str(e),
            )
            message.senderType = communication_pb2.SENDER_TYPE_UNSPECIFIED

        # Set the data of the message as a protobuf Struct (supports any JSON structure)
        try:
            if self.data is not None:
                # Convert the dictionary data to a protobuf Struct
                data_struct = struct_pb2.Struct()
                data_struct.update(self.data)
                message.data.CopyFrom(data_struct)
        except Exception as e:
            logger.error("Error setting data", value=repr(self.data), error=str(e))

        # Set the workflow ID
        try:
            message.workflowId = self.workflowId if self.workflowId is not None else ""
        except Exception as e:
            logger.error("Error setting workflowId", value=repr(self.workflowId), error=str(e))

        # Set the message status
        try:
            if self.status is not None:
                message.status = communication_pb2.MessageStatus.Value(self.status)
        except Exception as e:
            logger.warning(
                "Invalid status for protobuf enum, leaving unset",
                status=str(self.status),
                error=str(e),
            )

        # Set the message type
        try:
            message.type = communication_pb2.MessageType.Value(self.type)
        except Exception as e:
            logger.warning(
                "Invalid type for protobuf enum, defaulting to MESSAGE_TYPE_UNSPECIFIED",
                type=str(self.type),
                error=str(e),
            )
            message.type = communication_pb2.MESSAGE_TYPE_UNSPECIFIED

        from datetime import datetime as dt

        # createdAt
        try:
            if isinstance(self.createdAt, dt):
                message.createdAt.FromDatetime(self.createdAt)
            elif self.createdAt is not None:
                try:
                    message.createdAt.FromDatetime(dt.fromisoformat(str(self.createdAt)))
                except Exception as e2:
                    logger.warning(
                        "Invalid createdAt, using current time",
                        createdAt=str(self.createdAt),
                        error=str(e2),
                    )
                    message.createdAt.FromDatetime(dt.utcnow())
            else:
                message.createdAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error("Error setting createdAt", value=repr(self.createdAt), error=str(e))

        # updatedAt
        try:
            if isinstance(self.updatedAt, dt):
                message.updatedAt.FromDatetime(self.updatedAt)
            elif self.updatedAt is not None:
                try:
                    message.updatedAt.FromDatetime(dt.fromisoformat(str(self.updatedAt)))
                except Exception as e2:
                    logger.warning(
                        "Invalid updatedAt, using current time",
                        updatedAt=str(self.updatedAt),
                        error=str(e2),
                    )
                    message.updatedAt.FromDatetime(dt.utcnow())
            else:
                message.updatedAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error("Error setting updatedAt", value=repr(self.updatedAt), error=str(e))

        # Set the workflowResponse array field robustly
        try:
            wf_responses = self.workflowResponse if isinstance(self.workflowResponse, list) else []

            for idx, value in enumerate(wf_responses):
                try:
                    # Convert dict to protobuf Struct for JSON data
                    if isinstance(value, dict):
                        workflow_struct = struct_pb2.Struct()
                        workflow_struct.update(value)
                        message.workflowResponse.append(workflow_struct)
                    else:
                        logger.warning(
                            "workflowResponse value is not a dict and was skipped",
                            index=idx,
                            value_type=str(type(value)),
                        )
                except Exception as e:
                    logger.error(
                        "Failed to convert workflowResponse value to Struct",
                        index=idx,
                        value_repr=repr(value),
                        value_type=str(type(value)),
                        error=str(e),
                    )
        except Exception as e:
            logger.error(
                "Error setting workflowResponse",
                value=repr(self.workflowResponse),
                error=str(e),
            )

        # Return the message message
        return message
